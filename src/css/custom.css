/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #2e8555;
  --ifm-color-primary-dark: #29784c;
  --ifm-color-primary-darker: #277148;
  --ifm-color-primary-darkest: #205d3b;
  --ifm-color-primary-light: #33925d;
  --ifm-color-primary-lighter: #359962;
  --ifm-color-primary-lightest: #3cad6e;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* Fix blog post TOC width issue */
.blog-wrapper .col--3 {
  --ifm-col-width: calc(3 / 12 * 100%);
  flex: 0 0 var(--ifm-col-width);
  margin-left: 0;
  max-width: var(--ifm-col-width);
}

/* Ensure blog post TOC container fills the available space */
.blog-wrapper .table-of-contents {
  width: 100%;
  max-width: 100%;
}

/* Fix blog post layout to prevent right margin issues */
.blog-wrapper .container {
  max-width: 100%;
  padding-right: var(--ifm-spacing-horizontal);
  padding-left: var(--ifm-spacing-horizontal);
}

.blog-wrapper .row {
  margin-right: 0;
  margin-left: 0;
}

/* Additional TOC styling for blog posts */
.blog-wrapper .theme-doc-toc-desktop {
  width: 100%;
}

.blog-wrapper .theme-doc-toc-desktop .table-of-contents {
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}

/* Ensure proper spacing for blog post content */
.blog-wrapper .col {
  padding-left: var(--ifm-spacing-horizontal);
  padding-right: var(--ifm-spacing-horizontal);
}

/* Fix blog post sidebar width on larger screens */
@media (min-width: 997px) {
  .blog-wrapper .col--3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .blog-wrapper .col--9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
}

/* Responsive adjustments for smaller screens */
@media (max-width: 996px) {
  .blog-wrapper .col--3 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .blog-wrapper .col--9 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* More specific targeting for blog post TOC */
.blog-post-page .col--3,
.blog-wrapper .col--3 {
  --ifm-col-width: calc(3 / 12 * 100%);
  flex: 0 0 var(--ifm-col-width) !important;
  max-width: var(--ifm-col-width) !important;
  padding-right: var(--ifm-spacing-horizontal) !important;
}

/* Ensure TOC fills the sidebar completely */
.blog-post-page .table-of-contents,
.blog-wrapper .table-of-contents {
  width: 100% !important;
  max-width: 100% !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* Fix the main content area */
.blog-post-page .col--9,
.blog-wrapper .col--9 {
  flex: 0 0 75% !important;
  max-width: 75% !important;
}
