import React from 'react';
import Layout from '@theme-original/BlogPostItem/Layout';
import { createPortal } from 'react-dom';
import MentalModalTools from '../../../components/MentalModalTools';

export default function BlogPostItemLayoutWrapper(props) {
  const [tocContainer, setTocContainer] = React.useState(null);

  React.useEffect(() => {
    const findTocContainer = () => {
      const toc = document.querySelector('.table-of-contents');
      if (toc && toc.parentElement) {
        // 创建一个容器来放置我们的按钮
        let buttonContainer = document.querySelector('.custom-toc-buttons-container');
        if (!buttonContainer) {
          buttonContainer = document.createElement('div');
          buttonContainer.className = 'custom-toc-buttons-container';
          toc.parentElement.appendChild(buttonContainer);
        }
        setTocContainer(buttonContainer);
      }
    };

    // 延迟查找确保 DOM 已渲染
    const timer = setTimeout(findTocContainer, 100);

    return () => {
      clearTimeout(timer);
      setTocContainer(null);
    };
  }, []);

  return (
    <>
      <Layout {...props} />
      {tocContainer &&
        createPortal(
          <MentalModalTools metadata={props.frontMatter} />,
          tocContainer
        )
      }
    </>
  );
}
